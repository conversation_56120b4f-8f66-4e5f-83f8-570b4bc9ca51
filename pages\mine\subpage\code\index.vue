<template>
  <view class="all back-eee">
    <div>{{ proportion }}</div>
    <wd-button @click="proportion = 0">0</wd-button>
    <wd-button @click="proportion = 50">50</wd-button>
    <wd-button @click="proportion = 100">100</wd-button>

    <wd-datetime-picker type="date" v-model="value" :default-value="defaultValue" label="日期选择" @confirm="handleConfirm" />
  </view>
  <ActionSheet :is-dragging="true" class="mm" v-model="proportion" :anchors="[10, 50, 80]" @draggingEnd="handleDraggingEnd">
    <template v-for="item in 99" :key="item">
      <div>{{ item }}</div>
    </template>
  </ActionSheet>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import ActionSheet from '@/components/ActionSheet/index.vue'

const proportion = ref(50)

function handleDraggingEnd(value) {
  console.log(value)
}

const value = ref('')
const defaultValue = ref(Date.now())

function handleConfirm({ value }) {
  console.log(new Date(value))
}
</script>

<style lang="less" scoped>
.mm {
}
</style>
