import { nodeRequest } from '../index'

class PumpHouse {
  list(params) {
    return nodeRequest.get('/nodeServer/pumpHouse/wxlist', { params })
  }
  create(data) {
    return nodeRequest.post('/nodeServer/pumpHouse', data)
  }
  update(data) {
    return nodeRequest.put('/nodeServer/pumpHouse', data)
  }
  detail(pumpRoomNumber) {
    return nodeRequest.get(`/nodeServer/pumpHouse/detail/${pumpRoomNumber}`)
  }

  nodelist(pumpRoomNumber) {
    return nodeRequest.get(`/nodeServer/pumpHouse/nodelist/${pumpRoomNumber}`)
  }

  createNode(data) {
    return nodeRequest.post('/nodeServer/pumpHouse/node', data)
  }
  updateNode(data) {
    return nodeRequest.put('/nodeServer/pumpHouse/node', data)
  }

  // nodeList(pumpRoomNumber) {
  //   return nodeRequest.get(`/nodeServer/pumpHouse/node/list/${pumpRoomNumber}`)
  // }
  nodeDetail(pumpRoomNumber, nodeNum) {
    return nodeRequest.get(`/nodeServer/pumpHouse/node/${pumpRoomNumber}?Node=${nodeNum}`)
  }
  seek(pumpHouseName) {
    return nodeRequest.get(`/nodeServer/pumpHouse/seek?pumpHouseName=${pumpHouseName}`)
  }
  seekScope(ZoneCode) {
    return nodeRequest.get(`/nodeServer/pumpHouse/seekScope?ZoneCode=${ZoneCode}`)
  }
  pendingList(params) {
    return nodeRequest.get(`/nodeServer/pumpHouse/task/pendingList`, { params })
  }
  taskList(params) {
    return nodeRequest.get(`/nodeServer/pumpHouse/task/list`, { params })
  }
  inspectionPoint(id) {
    return nodeRequest.get(`/nodeServer/pumpHouse/task/inspectionPoint?TaskID=${id}`)
  }
  performance(id) {
    return nodeRequest.get(`/nodeServer/pumpHouse/task/performance?TaskID=${id}`)
  }
  tackCreate(data) {
    return nodeRequest.post(`/nodeServer/pumpHouse/task`, data)
  }
  tackRecord(data) {
    return nodeRequest.post(`/nodeServer/pumpHouse/task/record`, data)
  }
  tackRecordDetail(params) {
    return nodeRequest.get(`/nodeServer/pumpHouse/task/recordDetail`, { params })
  }
  tackDetaul(id) {
    return nodeRequest.get(`/nodeServer/pumpHouse/task?TaskID=${id}`)
  }
}

export const PumpHouseApi = new PumpHouse()
